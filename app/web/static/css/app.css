/* Signalsd UI Styles */

/* CSS Reset and Base Styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.5;
    color: #111827;
    background-color: #f9fafb;
    min-height: 100vh;
}

/* Layout */
.container {
    max-width: 80rem;
    margin: 0 auto;
    padding: 0 1rem;
}

.container-sm {
    max-width: 28rem;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Navigation */
.navigation {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 80rem;
    margin: 0 auto;
    padding: 0 1rem;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4rem;
}

.nav-brand {
    display: flex;
    align-items: center;
}

.nav-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
}

.nav-links {
    display: none;
    margin-left: 1.5rem;
    gap: 2rem;
}

.nav-link {
    color: #6b7280;
    text-decoration: none;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s;
}

.nav-link:hover {
    color: #374151;
}

.nav-link.active {
    color: #111827;
}

.nav-actions {
    display: flex;
    align-items: center;
}

/* Show nav links on medium screens and up */
@media (min-width: 768px) {
    .nav-links {
        display: flex;
    }
}

/* Forms */
.form-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
}

.form-card {
    width: 100%;
    max-width: 28rem;
    background: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-title {
    margin: 0 0 2rem 0;
    text-align: center;
    font-size: 1.875rem;
    font-weight: 800;
    color: #111827;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group-stacked {
    margin-bottom: 0;
}

.form-group-stacked:not(:last-child) {
    margin-bottom: -1px;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.form-label-sr {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    font-size: 0.875rem;
    color: #111827;
    background: white;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input-stacked {
    border-radius: 0;
    position: relative;
    z-index: 1;
}

.form-input-stacked:first-child {
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.form-input-stacked:last-child {
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.form-input-stacked:focus {
    z-index: 10;
}

.form-select {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #111827;
    background: white;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-select:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #6366f1;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #4f46e5;
}

.btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
    background: #e5e7eb;
}

.btn-full {
    width: 100%;
}

/* Cards */
.card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
}

/* Page Layout */
.page-container {
    max-width: 80rem;
    margin: 0 auto;
    padding: 1.5rem 1rem;
}

.page-title {
    margin: 0 0 1.5rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
}

/* Grid */
.grid {
    display: grid;
    gap: 1rem;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
}

@media (min-width: 768px) {
    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
    }

    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Spacing */
.space-y-4 > * + * {
    margin-top: 1rem;
}

.space-y-6 > * + * {
    margin-top: 1.5rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    border: 1px solid;
}

.alert-error {
    background: #fef2f2;
    border-color: #fecaca;
    color: #b91c1c;
}

.alert-success {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-info {
    background: #eff6ff;
    border-color: #bfdbfe;
    color: #1d4ed8;
}

.alert-warning {
    background: #fffbeb;
    border-color: #fed7aa;
    color: #d97706;
}

/* HTMX Loading States */
.htmx-indicator {
    opacity: 0;
    transition: opacity 500ms ease-in;
}

.htmx-request .htmx-indicator {
    opacity: 1;
}

.htmx-request.htmx-indicator {
    opacity: 1;
}

.htmx-request button[type="submit"] {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Signal Results */
.signal-card {
    background: #f9fafb;
    border-radius: 0.5rem;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    transition: box-shadow 0.2s;
}

.signal-card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.signal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.signal-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.5rem 0;
}

.signal-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.625rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.signal-badge-withdrawn {
    background: #fef2f2;
    color: #b91c1c;
}

.signal-metadata {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1rem;
    font-size: 0.875rem;
    color: #6b7280;
}

@media (min-width: 768px) {
    .signal-metadata {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .signal-metadata {
        grid-template-columns: repeat(3, 1fr);
    }
}

.signal-metadata-item {
    display: flex;
    flex-direction: column;
}

.signal-metadata-label {
    font-weight: 500;
    color: #111827;
}

.signal-metadata-value {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* JSON Display */
.json-container {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    margin-top: 1rem;
}

.json-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f9fafb;
}

.json-content {
    padding: 1rem;
    font-size: 0.875rem;
    color: #374151;
    overflow-x: auto;
    white-space: pre-wrap;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    max-height: 400px;
    overflow-y: auto;
    line-height: 1.4;
}

.json-content.pretty-printed {
    color: #111827;
    line-height: 1.6;
}

.json-content::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.json-content::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 4px;
}

.json-content::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
}

.json-content::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Correlation and Version Info */
.correlation-info {
    margin-top: 0.75rem;
    padding: 0.5rem;
    background: #eff6ff;
    border-radius: 0.375rem;
    border-left: 4px solid #3b82f6;
}

.correlated-signals {
    margin-top: 1rem;
    padding: 0.75rem;
    background: #eff6ff;
    border-radius: 0.375rem;
}

.previous-versions {
    margin-top: 1rem;
    padding: 0.75rem;
    background: #f3f4f6;
    border-radius: 0.375rem;
}

.technical-details {
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid #e5e7eb;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.font-mono {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.text-sm {
    font-size: 0.875rem;
}

.text-xs {
    font-size: 0.75rem;
}

.hidden {
    display: none;
}

/* Checkbox Components */
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.checkbox-field {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-input {
    margin-right: 0.5rem;
}

/* Text Colors */
.text-muted {
    color: #6b7280;
}

.text-primary {
    color: #1e40af;
}

.text-secondary {
    color: #374151;
}

/* Spacing Utilities */
.mb-4 {
    margin-bottom: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mt-4 {
    margin-top: 1rem;
}

.ml-2 {
    margin-left: 0.5rem;
}
