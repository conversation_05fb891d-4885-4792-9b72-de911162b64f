package types

// =============================================================================
// DROPDOWN OPTIONS
// =============================================================================
// These types are used for UI dropdown components in templates and handlers

type IsnOption struct {
	Slug       string `json:"slug"`
	IsInUse    bool   `json:"is_in_use"`
	Visibility string `json:"visibility"`
}

type SignalTypeOption struct {
	Slug string `json:"slug"`
}

type VersionOption struct {
	Version string `json:"version"`
}

type ServiceAccountOption struct {
	ClientOrganization string `json:"client_organization"`
	ClientContactEmail string `json:"client_contact_email"`
}

type UserOption struct {
	Email    string `json:"email"`
	UserRole string `json:"user_role"`
}

// AccountIdentifierOption represents an option for account identifier dropdowns
// Used for both user emails and service account client IDs
type AccountIdentifierOption struct {
	Value       string `json:"value"`        // The actual identifier (email or client_id)
	DisplayText string `json:"display_text"` // What to show in the dropdown
}

// AccountIdentifierOption represents an option for account identifier dropdowns
// Used for both user emails and service account client IDs
type AccountIdentifierOption struct {
	Value       string `json:"value"`        // The actual identifier (email or client_id)
	DisplayText string `json:"display_text"` // What to show in the dropdown
}
