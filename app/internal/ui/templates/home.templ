package templates


// =============================================================================
// LOGIN & REGISTRATION
// =============================================================================
templ LoginPage() {
	@BaseLayout("Login") {
		<div class="form-container">
			<div class="form-card">
				<h2 class="form-title">
					Sign in to Signal ISN
				</h2>

				<!-- Error message area - positioned prominently above the form -->
				<div id="login-error"></div>

				<form hx-post="/login" hx-target="#login-error">
					<div class="form-group">
						<div class="form-group-stacked">
							<label for="email" class="form-label-sr">Email address</label>
							<input
								id="email"
								name="email"
								type="email"
								required
								class="form-input form-input-stacked"
								placeholder="Email address"
							/>
						</div>
						<div class="form-group-stacked">
							<label for="password" class="form-label-sr">Password</label>
							<input
								id="password"
								name="password"
								type="password"
								required
								class="form-input form-input-stacked"
								placeholder="Password"
							/>
						</div>
					</div>
					<div class="form-group">
						<button type="submit" class="btn btn-primary btn-full">
							Sign in
						</button>
					</div>
				</form>
				<div class="form-group text-center">
					<p class="text-muted">Don't have an account?</p>
					<button
						hx-get="/register"
						hx-target="body"
						hx-swap="outerHTML"
						class="btn btn-secondary"
					>
						Create Account
					</button>
				</div>
			</div>

		</div>
	}
}

// REGISTER

templ RegisterPage() {
	@BaseLayout("Register") {
		<div class="form-container">
			<div class="form-card">
				<h2 class="form-title">
					Create Account
				</h2>
				<form hx-post="/register" hx-target="#register-error">
					<div class="form-group">
						<div class="form-group-stacked">
							<label for="reg-email" class="form-label-sr">Email address</label>
							<input
								id="reg-email"
								name="email"
								type="email"
								required
								class="form-input form-input-stacked"
								placeholder="Email address"
							/>
						</div>
						<div class="form-group-stacked">
							<label for="reg-password" class="form-label-sr">Password</label>
							<input
								id="reg-password"
								name="password"
								type="password"
								required
								minlength="11"
								class="form-input form-input-stacked"
								placeholder="Password (minimum 11 characters)"
							/>
						</div>
						<div class="form-group-stacked">
							<label for="reg-confirm-password" class="form-label-sr">Confirm Password</label>
							<input
								id="reg-confirm-password"
								name="confirm-password"
								type="password"
								required
								class="form-input form-input-stacked"
								placeholder="Confirm Password"
							/>
						</div>
					</div>
					<div class="form-group">
						<button type="submit" class="btn btn-primary btn-full">
							Create Account
						</button>
					</div>
				</form>
				<div id="register-error"></div>
				<div class="form-group text-center">
					<p class="text-muted">Already have an account?</p>
					<button
						hx-get="/login"
						hx-target="body"
						hx-swap="outerHTML"
						class="btn btn-secondary"
					>
						Sign In
					</button>
				</div>
			</div>
		</div>
	}
}

// REGISTRATION SUCCESS

templ RegistrationSuccess() {
	<div class="alert alert-success">
		<strong>Account created!</strong> You can now sign in.
		<div class="form-group text-center" style="margin-top: 1rem;">
			<button
				hx-get="/login"
				hx-target="body"
				hx-swap="outerHTML"
				class="btn btn-primary"
			>
				Continue to Sign In
			</button>
		</div>
	</div>
	<script>
		// Auto-redirect to login after 5 seconds
		setTimeout(function() {
			htmx.ajax('GET', '/login', {target: 'body', swap: 'outerHTML'});
		}, 5000);
	</script>
}
	
