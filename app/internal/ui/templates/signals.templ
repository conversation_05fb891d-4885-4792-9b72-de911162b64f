package templates

import (
    "strings"
	"fmt"
	"github.com/information-sharing-networks/signalsd/app/internal/ui/client"
	"github.com/information-sharing-networks/signalsd/app/internal/ui/types"
)
// =============================================================================
// SIGNAL SEARCH PAGE & RESULTS
// =============================================================================
templ SignalSearchPage(isns []types.IsnOption, perms map[string]types.IsnPerm, results []client.SearchSignalWithCorrelationsAndVersions) {
	@BaseLayout("Signal Search") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">Search Signals</h1>
				<div class="card mb-6">
					<div class="card-body">
						<form hx-post="/ui-api/search-signals" hx-target="#search-results" class="space-y-4">
							<div class="grid grid-cols-1 md:grid-cols-3">
								<div class="form-group">
									<label for="isn-slug" class="form-label">ISN</label>
									<select
										id="isn-slug"
										name="isn-slug"
										required
										hx-post="/ui-api/signal-type-options"
										hx-target="#signal-type-slug"
										hx-swap="outerHTML"
										hx-trigger="change"
										hx-include="#isn-slug, this"
										class="form-select"
									>
										<option value="">Select ISN...</option>
										if isns != nil {
											for _, isn := range isns {
												if isn.IsInUse {
													<option value={ isn.Slug }>{ strings.ReplaceAll(isn.Slug, "-", " ") }</option>
												}
											}
										}
									</select>
								</div>
							<div id="signal-type-select" class="form-group">
								<label for="signal-type-slug" class="form-label">Signal Type</label>
								<select
									id="signal-type-slug"
									name="signal-type-slug"
									required
									hx-post="/ui-api/signal-type-version-options"
									hx-target="#sem-ver"
									hx-swap="outerHTML"
									hx-trigger="change"
									hx-include="#isn-slug, this"
									class="form-select"
								>
									<option value="">Select Signal Type...</option>
								</select>
							</div>
							<div id="version-select" class="form-group">
								<label for="sem-ver" class="form-label">Version</label>
								<select
									id="sem-ver"
									name="sem-ver"
									required
									class="form-select"
								>
									<option value="">Select Version...</option>
								</select>
							</div>
						</div>
						<div class="grid grid-cols-1 md:grid-cols-2">
							<div class="form-group">
								<label for="start-date" class="form-label">Start Date</label>
								<input
									type="date"
									id="start-date"
									name="start-date"
									class="form-input"
								/>
							</div>
							<div class="form-group">
								<label for="end-date" class="form-label">End Date</label>
								<input
									type="date"
									id="end-date"
									name="end-date"
									class="form-input"
								/>
							</div>
						</div>
						<div class="grid grid-cols-1 md:grid-cols-3">
							<div class="form-group">
								<label for="local-ref" class="form-label">Local Reference</label>
								<input
									type="text"
									id="local-ref"
									name="local-ref"
									placeholder="e.g., item_id_#1"
									class="form-input"
								/>
							</div>
							<div class="form-group">
								<label for="signal-id" class="form-label">Signal ID</label>
								<input
									type="text"
									id="signal-id"
									name="signal-id"
									placeholder="UUID"
									class="form-input"
								/>
							</div>
							<div class="form-group">
								<label for="account-id" class="form-label">Account ID</label>
								<input
									type="text"
									id="account-id"
									name="account-id"
									placeholder="UUID"
									class="form-input"
								/>
							</div>
						</div>
						<div class="checkbox-group">
							@CheckboxField("include-withdrawn", "true", "Include withdrawn signals")
							@CheckboxField("include-correlated", "true", "Include correlated signals")
							@CheckboxField("include-previous-versions", "true", "Include previous versions")
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary">
								Search Signals
							</button>
						</div>
					</form>
				</div>
			</div>
			<div id="search-results">

				if results != nil {
					@SearchResults(results)
				}
			</div>
		</div>
	}
}



// Search Results renders the search results
templ SearchResults(signals []client.SearchSignalWithCorrelationsAndVersions) {
	<div class="card">
		<div class="card-header">
			<h3 class="card-title">Search Results ({ fmt.Sprintf("%d", len(signals)) } signals found)</h3>
		</div>
		if len(signals) == 0 {
			<div class="card-body text-center text-muted">
				No signals found matching your search criteria.
			</div>
		} else {
			<div class="card-body space-y-6">
				for _, signal := range signals {
					<div class="signal-card">
						<!-- Signal Header -->
						<div class="signal-header">
							<div style="flex: 1;">
								<div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 0.5rem;">
									if signal.LocalRef != "" {
										<h4 class="signal-title">{ signal.LocalRef }</h4>
									} else {
										<h4 class="signal-title">Signal { signal.SignalID[:8] }...</h4>
									}
									if signal.IsWithdrawn {
										<span class="signal-badge signal-badge-withdrawn">
											Withdrawn
										</span>
									}
								</div>
								<div class="signal-metadata">
									@SignalMetadataItem("Signal ID", signal.SignalID)
									@SignalMetadataItem("Version ID", signal.SignalVersionID)
									@SignalMetadataSimple("Version", fmt.Sprintf("%d", signal.VersionNumber))
									@SignalMetadataSimple("Created", signal.SignalCreatedAt)
									@SignalMetadataSimple("Updated", signal.VersionCreatedAt)
									if signal.Email != "" {
										@SignalMetadataSimple("Created by", signal.Email)
									}
								</div>
								if signal.CorrelatedToSignalID != "" {
									<div class="correlation-info">
										<span class="text-sm" style="color: #1e40af;">
											<span style="font-weight: 500;">Correlated to:</span> { signal.CorrelatedToSignalID }
										</span>
									</div>
								}
							</div>
						</div>
						<!-- Signal Content -->
						<div style="margin-top: 1rem;">
							<div class="json-header">
								<h5 class="text-sm" style="font-weight: 500; color: #111827;">Signal Content</h5>
								<button
									type="button"
									data-signal-id={ signal.SignalID }
									class="pretty-print-btn btn btn-secondary text-xs"
								>
									Pretty Print
								</button>
							</div>
							<div class="json-container">
								<pre id={ fmt.Sprintf("json-%s", signal.SignalID) } class="json-content">{ string(signal.Content) }</pre>
							</div>
						</div>
						<!-- Additional Information -->
						if len(signal.CorrelatedSignals) > 0 {
							<div class="correlated-signals">
								<h5 class="text-sm" style="font-weight: 500; color: #111827; margin-bottom: 0.5rem;">Correlated Signals ({ fmt.Sprintf("%d", len(signal.CorrelatedSignals)) })</h5>
								<div class="space-y-1">
									for _, correlated := range signal.CorrelatedSignals {
										<div class="text-sm" style="color: #374151;">
											<span class="font-mono">{ correlated.SignalID[:8] }...</span>
											if correlated.LocalRef != "" {
												<span style="margin-left: 0.5rem;">({ correlated.LocalRef })</span>
											}
											<span style="margin-left: 0.5rem; color: #6b7280;">v{ fmt.Sprintf("%d", correlated.VersionNumber) }</span>
										</div>
									}
								</div>
							</div>
						}
						if len(signal.PreviousSignalVersions) > 0 {
							<div class="previous-versions">
								<h5 class="text-sm" style="font-weight: 500; color: #111827; margin-bottom: 0.5rem;">Previous Versions ({ fmt.Sprintf("%d", len(signal.PreviousSignalVersions)) })</h5>
								<div class="space-y-1">
									for _, version := range signal.PreviousSignalVersions {
										<div class="text-sm" style="color: #374151;">
											<span style="font-weight: 500;">Version { fmt.Sprintf("%d", version.VersionNumber) }</span>
											<span style="margin-left: 0.5rem; color: #6b7280;">{ version.CreatedAt }</span>
										</div>
									}
								</div>
							</div>
						}
						<!-- Additional Technical Info -->
						if signal.AccountID != "" || signal.AccountType != "" {
							<div class="technical-details">
								<h6 class="text-xs" style="font-weight: 500; color: #6b7280; margin-bottom: 0.5rem;">Additional Information</h6>
								<div class="signal-metadata text-xs" style="color: #6b7280;">
									if signal.AccountID != "" {
										<div><span style="font-weight: 500;">Account ID:</span> <span class="font-mono">{ signal.AccountID }</span></div>
									}
									if signal.AccountType != "" {
										<div><span style="font-weight: 500;">Account Type:</span> { signal.AccountType }</div>
									}
								</div>
							</div>
						}
					</div>
				}
			</div>
		}
	</div>
	<script>

		// Store original JSON content for each signal
		const originalJsonContent = new Map();

		document.addEventListener('click', function(e) {
		if (!e.target.classList.contains('pretty-print-btn')) {
			return
		}

		const signalId = e.target.getAttribute('data-signal-id');
		const jsonElement = document.getElementById('json-' + signalId);

		if (!jsonElement) {
			return;
		}

		try {
			const currentButtonText = e.target.textContent.trim();

			if (currentButtonText === 'Pretty Print') {
				let originalContent = originalJsonContent.get(signalId);
				if (!originalContent) {
					originalContent = jsonElement.textContent.trim();
					originalJsonContent.set(signalId, originalContent);
				}
				// Parse and pretty print JSON
				const parsed = JSON.parse(originalContent);
				jsonElement.textContent = JSON.stringify(parsed, null, 2);
				e.target.textContent = 'Compact';
				jsonElement.classList.add('pretty-printed');
			} else {
				let originalContent = originalJsonContent.get(signalId);
				jsonElement.textContent = originalContent;
				e.target.textContent = 'Pretty Print';
				jsonElement.classList.remove('pretty-printed');
			}
		} catch (error) {
			// Show error message briefly
			const originalText = e.target.textContent;
			e.target.textContent = 'Invalid JSON';
			e.target.classList.add('text-red-600');
			setTimeout(function() {
				e.target.textContent = originalText;
				e.target.classList.remove('text-red-600');
			}, 2000);
			}
		});
	</script>
}
