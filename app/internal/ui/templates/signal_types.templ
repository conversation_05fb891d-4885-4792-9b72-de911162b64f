package templates
import (
    "github.com/information-sharing-networks/signalsd/app/internal/ui/client"
	"github.com/information-sharing-networks/signalsd/app/internal/ui/types"
)

// CreateSignalTypePage renders the signal type management page
templ CreateSignalTypePage(isns []types.IsnOption) {
	@BaseLayout("Create New Signal Type") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">Create New Signal Type</h1>
			<div class="card mb-6">
				<div class="card-body">
					<form hx-post="/ui-api/create-signal-type" hx-target="#signal-type-result" class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div class="form-group">
								<label for="isn-slug" class="form-label">ISN</label>
								<select
									id="isn-slug"
									name="isn-slug"
									required
									class="form-select"
								>
									<option value="">Select an ISN</option>
									for _, isn := range isns {
										<option value={ isn.Slug }>{ isn.Slug }</option>
									}
								</select>
							</div>
							<div class="form-group">
								<label for="bump-type" class="form-label">Version</label>
								<select
									id="bump-type"
									name="bump-type"
									required
									class="form-select"
								>
									<option value="">Select version</option>
									<option value="patch">Patch (0.0.1)</option>
									<option value="minor">Minor (0.1.0)</option>
									<option value="major">Major (1.0.0)</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label for="title" class="form-label">Title</label>
							<input
								id="title"
								name="title"
								type="text"
								required
								class="form-input"
								placeholder="e.g., Sample Signal @example.org"
							/>
							<p class="text-muted text-sm mt-1">Unique title for the signal type. This will be used to create a URL-friendly slug.</p>
						</div>
						<div class="form-group">
							<label for="schema-url" class="form-label">Schema URL</label>
							<input
								id="schema-url"
								name="schema-url"
								type="url"
								required
								class="form-input"
								placeholder="https://github.com/user/project/blob/2025.01.01/schema.json"
							/>
							<p class="text-muted text-sm mt-1">
								Must be a valid JSON schema available on Github
								<code class="text-xs bg-gray-100 px-1 py-0.5 rounded">https://github.com/skip/validation/main/schema.json</code>
								to disable validation.
							</p>
						</div>
						<div class="form-group">
							<label for="readme-url" class="form-label">README URL</label>
							<input
								id="readme-url"
								name="readme-url"
								type="url"
								class="form-input"
								placeholder="https://github.com/user/project/blob/2025.01.01/readme.md"
							/>
							<p class="text-muted text-sm mt-1">Must be a GitHub URL ending in .md. Use
								<code class="text-xs bg-gray-100 px-1 py-0.5 rounded">https://github.com/skip/readme/main/readme.md</code>
								to indicate there is no readme
							</p>

						</div>
						<div class="form-group">
							<label for="detail" class="form-label">Description</label>
							<textarea
								id="detail"
								name="detail"
								rows="3"
								class="form-input"
								placeholder="Description of the signal type"
							></textarea>
							<p class="text-muted text-sm mt-1">Provide a detailed description of this signal type.</p>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary">
								Create Signal Type
							</button>
						</div>
					</form>
				</div>
			</div>
			<div id="signal-type-result"></div>
		</div>
	}
}

// SignalTypeCreationSuccess renders a success message after signal type creation
templ SignalTypeCreationSuccess(response client.CreateSignalTypeResponse) {
	<div class="card">
		<div class="card-body">
			@SuccessAlert("Signal type created successfully!")
			<div class="mt-4 space-y-2">
				<p><strong>Slug:</strong> <code class="text-sm bg-gray-100 px-2 py-1 rounded">{ response.Slug }</code></p>
				<p><strong>Version:</strong> <code class="text-sm bg-gray-100 px-2 py-1 rounded">{ response.SemVer }</code></p>
				<p><strong>Resource URL:</strong></p>
				<code class="text-xs bg-gray-100 px-2 py-1 rounded block break-all">{ response.ResourceURL }</code>
			</div>
			<div class="mt-4">
				<button
					hx-get="/admin/signal-types"
					hx-target="body"
					hx-swap="outerHTML"
					class="btn btn-primary"
				>
					Create Another Signal Type
				</button>
			</div>
		</div>
	</div>
}