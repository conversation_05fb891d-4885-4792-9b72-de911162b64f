
package templates

templ AdminDashboardPage() {
	@BaseLayout("ISN Administration") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">ISN Administration</h1>
			<p class="text-muted mb-6">Manage Information Sharing Networks and user access.</p>

			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

				<!-- ISN Configuration -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">ISN Configuration</h3>
						<p class="text-muted mb-4">Create new Information Sharing Networks.</p>
						<a href="/admin/isns" class="btn btn-primary">Manage ISNs</a>
					</div>
				</div>

				<!-- Account Management -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">Account Management</h3>
						<p class="text-muted mb-4">Add and manage user access to ISNs.</p>
						<a href="/admin/isn-accounts" class="btn btn-primary">Manage ISN Accounts</a>
					</div>
				</div>

				<!-- Signal Types -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">Signal Types</h3>
						<p class="text-muted mb-4">Configure the signal types used in an ISN.</p>
						<a href="/admin/signal-types" class="btn btn-primary">Manage Signal Types</a>
					</div>
				</div>

				<!-- User Management -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">User Management</h3>
						<p class="text-muted mb-4">Manage user accounts and permissions.</p>
						<a href="/admin/users" class="btn btn-primary">Manage Users</a>
					</div>
				</div>

				<!-- Service Accounts -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">Service Accounts</h3>
						<p class="text-muted mb-4">Manage API service accounts and credentials.</p>
						<a href="/admin/service-accounts" class="btn btn-primary">Manage Signal Types</a>
					</div>
				</div>

				<!-- System Settings -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">System Settings</h3>
						<p class="text-muted mb-4">Configure system-wide settings and preferences.</p>
						<button class="btn btn-secondary" disabled>System Settings</button>
						<p class="text-xs text-muted mt-2">Coming soon</p>
					</div>
				</div>
			</div>

		</div>
	}
}