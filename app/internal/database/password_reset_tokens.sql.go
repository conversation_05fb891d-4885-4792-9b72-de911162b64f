// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.30.0
// source: password_reset_tokens.sql

package database

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const CountActivePasswordResetTokensForUser = `-- name: CountActivePasswordResetTokensForUser :one
SELECT COUNT(*) AS active_tokens 
FROM password_reset_tokens
WHERE user_account_id = $1
AND expires_at > NOW()
`

// used for rate limiting and testing
func (q *Queries) CountActivePasswordResetTokensForUser(ctx context.Context, userAccountID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, CountActivePasswordResetTokensForUser, userAccountID)
	var active_tokens int64
	err := row.Scan(&active_tokens)
	return active_tokens, err
}

const CreatePasswordResetToken = `-- name: CreatePasswordResetToken :one
INSERT INTO password_reset_tokens (id, user_account_id, created_at, expires_at, created_by_admin_id)
VALUES ($1, $2, NOW(), $3, $4)
RETURNING id
`

type CreatePasswordResetTokenParams struct {
	ID               uuid.UUID `json:"id"`
	UserAccountID    uuid.UUID `json:"user_account_id"`
	ExpiresAt        time.Time `json:"expires_at"`
	CreatedByAdminID uuid.UUID `json:"created_by_admin_id"`
}

func (q *Queries) CreatePasswordResetToken(ctx context.Context, arg CreatePasswordResetTokenParams) (uuid.UUID, error) {
	row := q.db.QueryRow(ctx, CreatePasswordResetToken,
		arg.ID,
		arg.UserAccountID,
		arg.ExpiresAt,
		arg.CreatedByAdminID,
	)
	var id uuid.UUID
	err := row.Scan(&id)
	return id, err
}

const DeleteExpiredPasswordResetTokens = `-- name: DeleteExpiredPasswordResetTokens :execrows
DELETE FROM password_reset_tokens 
WHERE expires_at < NOW()
`

func (q *Queries) DeleteExpiredPasswordResetTokens(ctx context.Context) (int64, error) {
	result, err := q.db.Exec(ctx, DeleteExpiredPasswordResetTokens)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const DeletePasswordResetToken = `-- name: DeletePasswordResetToken :execrows
DELETE FROM password_reset_tokens 
WHERE id = $1
`

func (q *Queries) DeletePasswordResetToken(ctx context.Context, id uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, DeletePasswordResetToken, id)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const DeletePasswordResetTokensForUser = `-- name: DeletePasswordResetTokensForUser :execrows
DELETE FROM password_reset_tokens 
WHERE user_account_id = $1
`

func (q *Queries) DeletePasswordResetTokensForUser(ctx context.Context, userAccountID uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, DeletePasswordResetTokensForUser, userAccountID)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const GetPasswordResetToken = `-- name: GetPasswordResetToken :one
SELECT created_at, user_account_id, expires_at, created_by_admin_id
FROM password_reset_tokens
WHERE id = $1
`

type GetPasswordResetTokenRow struct {
	CreatedAt        time.Time `json:"created_at"`
	UserAccountID    uuid.UUID `json:"user_account_id"`
	ExpiresAt        time.Time `json:"expires_at"`
	CreatedByAdminID uuid.UUID `json:"created_by_admin_id"`
}

func (q *Queries) GetPasswordResetToken(ctx context.Context, id uuid.UUID) (GetPasswordResetTokenRow, error) {
	row := q.db.QueryRow(ctx, GetPasswordResetToken, id)
	var i GetPasswordResetTokenRow
	err := row.Scan(
		&i.CreatedAt,
		&i.UserAccountID,
		&i.ExpiresAt,
		&i.CreatedByAdminID,
	)
	return i, err
}

const GetPasswordResetTokensCreatedByAdmin = `-- name: GetPasswordResetTokensCreatedByAdmin :many
SELECT id, created_at, user_account_id, expires_at
FROM password_reset_tokens
WHERE created_by_admin_id = $1
ORDER BY created_at DESC
`

type GetPasswordResetTokensCreatedByAdminRow struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UserAccountID uuid.UUID `json:"user_account_id"`
	ExpiresAt     time.Time `json:"expires_at"`
}

// for admin audit queries
func (q *Queries) GetPasswordResetTokensCreatedByAdmin(ctx context.Context, createdByAdminID uuid.UUID) ([]GetPasswordResetTokensCreatedByAdminRow, error) {
	rows, err := q.db.Query(ctx, GetPasswordResetTokensCreatedByAdmin, createdByAdminID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetPasswordResetTokensCreatedByAdminRow
	for rows.Next() {
		var i GetPasswordResetTokensCreatedByAdminRow
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UserAccountID,
			&i.ExpiresAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
