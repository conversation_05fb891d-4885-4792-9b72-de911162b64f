// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.30.0
// source: signal_processing_failures.sql

package database

import (
	"context"

	"github.com/google/uuid"
)

const CreateSignalProcessingFailureDetail = `-- name: CreateSignalProcessingFailureDetail :one
INSERT INTO signal_processing_failures (
    signal_batch_id,
    signal_type_slug,
    signal_type_sem_ver,
    local_ref,
    error_code,
    error_message
) VALUES (
    $1, -- signal_batch_id
    $2, -- signal_type_slug
    $3, -- signal_type_sem_ver
    $4, -- local_ref
    $5, -- error_code
    $6  -- error_message
)
RETURNING id, created_at, signal_batch_id, signal_type_slug, signal_type_sem_ver, local_ref, error_code, error_message
`

type CreateSignalProcessingFailureDetailParams struct {
	SignalBatchID    uuid.UUID `json:"signal_batch_id"`
	SignalTypeSlug   string    `json:"signal_type_slug"`
	SignalTypeSemVer string    `json:"signal_type_sem_ver"`
	LocalRef         string    `json:"local_ref"`
	ErrorCode        string    `json:"error_code"`
	ErrorMessage     string    `json:"error_message"`
}

func (q *Queries) CreateSignalProcessingFailureDetail(ctx context.Context, arg CreateSignalProcessingFailureDetailParams) (SignalProcessingFailure, error) {
	row := q.db.QueryRow(ctx, CreateSignalProcessingFailureDetail,
		arg.SignalBatchID,
		arg.SignalTypeSlug,
		arg.SignalTypeSemVer,
		arg.LocalRef,
		arg.ErrorCode,
		arg.ErrorMessage,
	)
	var i SignalProcessingFailure
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.SignalBatchID,
		&i.SignalTypeSlug,
		&i.SignalTypeSemVer,
		&i.LocalRef,
		&i.ErrorCode,
		&i.ErrorMessage,
	)
	return i, err
}
