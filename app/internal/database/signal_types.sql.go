// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.30.0
// source: signal_types.sql

package database

import (
	"context"

	"github.com/google/uuid"
)

const CheckSignalTypeHasSignals = `-- name: CheckSignalTypeHasSignals :one
SELECT EXISTS(
    SELECT 1
    FROM signals
    WHERE signal_type_id = $1
) AS in_use
`

func (q *Queries) CheckSignalTypeHasSignals(ctx context.Context, signalTypeID uuid.UUID) (bool, error) {
	row := q.db.QueryRow(ctx, CheckSignalTypeHasSignals, signalTypeID)
	var in_use bool
	err := row.Scan(&in_use)
	return in_use, err
}

const CreateSignalType = `-- name: CreateSignalType :one

INSERT INTO signal_types (
    id,
    created_at,
    updated_at,
    isn_id,
    slug,
    schema_url,
    readme_url,
    title,
    detail,
    sem_ver,
    is_in_use,
    schema_content
    ) VALUES (gen_random_uuid(), now(), now(), $1, $2, $3, $4, $5, $6, $7, true, $8)
RETURNING id, created_at, updated_at, isn_id, slug, schema_url, readme_url, title, detail, sem_ver, is_in_use, schema_content
`

type CreateSignalTypeParams struct {
	IsnID         uuid.UUID `json:"isn_id"`
	Slug          string    `json:"slug"`
	SchemaURL     string    `json:"schema_url"`
	ReadmeURL     string    `json:"readme_url"`
	Title         string    `json:"title"`
	Detail        string    `json:"detail"`
	SemVer        string    `json:"sem_ver"`
	SchemaContent string    `json:"schema_content"`
}

func (q *Queries) CreateSignalType(ctx context.Context, arg CreateSignalTypeParams) (SignalType, error) {
	row := q.db.QueryRow(ctx, CreateSignalType,
		arg.IsnID,
		arg.Slug,
		arg.SchemaURL,
		arg.ReadmeURL,
		arg.Title,
		arg.Detail,
		arg.SemVer,
		arg.SchemaContent,
	)
	var i SignalType
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsnID,
		&i.Slug,
		&i.SchemaURL,
		&i.ReadmeURL,
		&i.Title,
		&i.Detail,
		&i.SemVer,
		&i.IsInUse,
		&i.SchemaContent,
	)
	return i, err
}

const DeleteSignalType = `-- name: DeleteSignalType :execrows
DELETE FROM signal_types
WHERE id = $1
`

func (q *Queries) DeleteSignalType(ctx context.Context, id uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, DeleteSignalType, id)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const ExistsSignalTypeWithSlugAndSchema = `-- name: ExistsSignalTypeWithSlugAndSchema :one
SELECT EXISTS
  (SELECT 1
   FROM signal_types
   WHERE slug = $1
   AND schema_url = $2) AS EXISTS
`

type ExistsSignalTypeWithSlugAndSchemaParams struct {
	Slug      string `json:"slug"`
	SchemaURL string `json:"schema_url"`
}

func (q *Queries) ExistsSignalTypeWithSlugAndSchema(ctx context.Context, arg ExistsSignalTypeWithSlugAndSchemaParams) (bool, error) {
	row := q.db.QueryRow(ctx, ExistsSignalTypeWithSlugAndSchema, arg.Slug, arg.SchemaURL)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const GetInUseSignalTypesByIsnID = `-- name: GetInUseSignalTypesByIsnID :many
SELECT st.id, st.created_at, st.updated_at, st.isn_id, st.slug, st.schema_url, st.readme_url, st.title, st.detail, st.sem_ver, st.is_in_use, st.schema_content
FROM signal_types st
WHERE st.isn_id = $1
AND is_in_use = true
`

// only returns active signal_types (is_in_use = true)
func (q *Queries) GetInUseSignalTypesByIsnID(ctx context.Context, isnID uuid.UUID) ([]SignalType, error) {
	rows, err := q.db.Query(ctx, GetInUseSignalTypesByIsnID, isnID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SignalType
	for rows.Next() {
		var i SignalType
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsnID,
			&i.Slug,
			&i.SchemaURL,
			&i.ReadmeURL,
			&i.Title,
			&i.Detail,
			&i.SemVer,
			&i.IsInUse,
			&i.SchemaContent,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetSemVerAndSchemaForLatestSlugVersion = `-- name: GetSemVerAndSchemaForLatestSlugVersion :one
SELECT '0.0.0' AS sem_ver,
       '' AS schema_url
WHERE NOT EXISTS
    (SELECT 1
     FROM signal_types st1
     WHERE st1.slug = $1)
UNION ALL
SELECT st2.sem_ver,
       st2.schema_url
FROM signal_types st2
WHERE st2.slug = $1
  AND st2.sem_ver =
    (SELECT max(st3.sem_ver)
     FROM signal_types st3
     WHERE st3.slug = $1)
`

type GetSemVerAndSchemaForLatestSlugVersionRow struct {
	SemVer    string `json:"sem_ver"`
	SchemaURL string `json:"schema_url"`
}

// if there are no signals defs for the supplied slug, this query returns an empty string for schema_url and a sem_ver of '0.0.0'
func (q *Queries) GetSemVerAndSchemaForLatestSlugVersion(ctx context.Context, slug string) (GetSemVerAndSchemaForLatestSlugVersionRow, error) {
	row := q.db.QueryRow(ctx, GetSemVerAndSchemaForLatestSlugVersion, slug)
	var i GetSemVerAndSchemaForLatestSlugVersionRow
	err := row.Scan(&i.SemVer, &i.SchemaURL)
	return i, err
}

const GetSignalTypeByIsnID = `-- name: GetSignalTypeByIsnID :many
SELECT 
    st.id, st.created_at, st.updated_at, st.isn_id, st.slug, st.schema_url, st.readme_url, st.title, st.detail, st.sem_ver, st.is_in_use, st.schema_content
FROM signal_types st
WHERE st.isn_id = $1
`

func (q *Queries) GetSignalTypeByIsnID(ctx context.Context, isnID uuid.UUID) ([]SignalType, error) {
	rows, err := q.db.Query(ctx, GetSignalTypeByIsnID, isnID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SignalType
	for rows.Next() {
		var i SignalType
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsnID,
			&i.Slug,
			&i.SchemaURL,
			&i.ReadmeURL,
			&i.Title,
			&i.Detail,
			&i.SemVer,
			&i.IsInUse,
			&i.SchemaContent,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetSignalTypeBySlug = `-- name: GetSignalTypeBySlug :one

SELECT st.id, st.created_at, st.updated_at, st.isn_id, st.slug, st.schema_url, st.readme_url, st.title, st.detail, st.sem_ver, st.is_in_use, st.schema_content
FROM signal_types st
WHERE st.slug = $1
AND st.sem_ver = $2
`

type GetSignalTypeBySlugParams struct {
	Slug   string `json:"slug"`
	SemVer string `json:"sem_ver"`
}

func (q *Queries) GetSignalTypeBySlug(ctx context.Context, arg GetSignalTypeBySlugParams) (SignalType, error) {
	row := q.db.QueryRow(ctx, GetSignalTypeBySlug, arg.Slug, arg.SemVer)
	var i SignalType
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsnID,
		&i.Slug,
		&i.SchemaURL,
		&i.ReadmeURL,
		&i.Title,
		&i.Detail,
		&i.SemVer,
		&i.IsInUse,
		&i.SchemaContent,
	)
	return i, err
}

const GetSignalTypes = `-- name: GetSignalTypes :many

SELECT st.id, st.created_at, st.updated_at, st.isn_id, st.slug, st.schema_url, st.readme_url, st.title, st.detail, st.sem_ver, st.is_in_use, st.schema_content
FROM signal_types st
`

func (q *Queries) GetSignalTypes(ctx context.Context) ([]SignalType, error) {
	rows, err := q.db.Query(ctx, GetSignalTypes)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SignalType
	for rows.Next() {
		var i SignalType
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsnID,
			&i.Slug,
			&i.SchemaURL,
			&i.ReadmeURL,
			&i.Title,
			&i.Detail,
			&i.SemVer,
			&i.IsInUse,
			&i.SchemaContent,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetSignalTypesByIsnID = `-- name: GetSignalTypesByIsnID :many

SELECT st.id, st.created_at, st.updated_at, st.isn_id, st.slug, st.schema_url, st.readme_url, st.title, st.detail, st.sem_ver, st.is_in_use, st.schema_content
FROM signal_types st
WHERE st.isn_id = $1
`

func (q *Queries) GetSignalTypesByIsnID(ctx context.Context, isnID uuid.UUID) ([]SignalType, error) {
	rows, err := q.db.Query(ctx, GetSignalTypesByIsnID, isnID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SignalType
	for rows.Next() {
		var i SignalType
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsnID,
			&i.Slug,
			&i.SchemaURL,
			&i.ReadmeURL,
			&i.Title,
			&i.Detail,
			&i.SemVer,
			&i.IsInUse,
			&i.SchemaContent,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateSignalTypeDetails = `-- name: UpdateSignalTypeDetails :execrows
UPDATE signal_types SET (updated_at, readme_url, detail, is_in_use) = (NOW(), $2, $3, $4)
WHERE id = $1
`

type UpdateSignalTypeDetailsParams struct {
	ID        uuid.UUID `json:"id"`
	ReadmeURL string    `json:"readme_url"`
	Detail    string    `json:"detail"`
	IsInUse   bool      `json:"is_in_use"`
}

func (q *Queries) UpdateSignalTypeDetails(ctx context.Context, arg UpdateSignalTypeDetailsParams) (int64, error) {
	result, err := q.db.Exec(ctx, UpdateSignalTypeDetails,
		arg.ID,
		arg.ReadmeURL,
		arg.Detail,
		arg.IsInUse,
	)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}
