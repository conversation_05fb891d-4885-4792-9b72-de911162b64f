// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.30.0
// source: reset.sql

package database

import (
	"context"
)

const DeleteAccounts = `-- name: DeleteAccounts :execrows
DELETE FROM Accounts
`

func (q *Queries) DeleteAccounts(ctx context.Context) (int64, error) {
	result, err := q.db.Exec(ctx, DeleteAccounts)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}
