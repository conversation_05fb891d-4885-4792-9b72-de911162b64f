// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.30.0
// source: users.sql

package database

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const CreateOwnerUser = `-- name: CreateOwnerUser :one
INSERT INTO users (account_id, created_at, updated_at, email, hashed_password, user_role)
VALUES ( $1, NOW(), NOW(), $2, $3, 'owner')
RETURNING account_id, created_at, updated_at, email, hashed_password, user_role
`

type CreateOwnerUserParams struct {
	AccountID      uuid.UUID `json:"account_id"`
	Email          string    `json:"email"`
	HashedPassword string    `json:"hashed_password"`
}

func (q *Queries) CreateOwnerUser(ctx context.Context, arg CreateOwnerUserParams) (User, error) {
	row := q.db.QueryRow(ctx, CreateOwnerUser, arg.AccountID, arg.Email, arg.Has<PERSON>assword)
	var i User
	err := row.Scan(
		&i.AccountID,
		&i.<PERSON>At,
		&i.UpdatedAt,
		&i.Email,
		&i.<PERSON>,
		&i.UserRole,
	)
	return i, err
}

const CreateUser = `-- name: CreateUser :one
INSERT INTO users (account_id, created_at, updated_at, email, hashed_password, user_role)
VALUES ( $1, NOW(), NOW(), $2, $3, 'member')
RETURNING account_id, created_at, updated_at, email, hashed_password, user_role
`

type CreateUserParams struct {
	AccountID      uuid.UUID `json:"account_id"`
	Email          string    `json:"email"`
	HashedPassword string    `json:"hashed_password"`
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, CreateUser, arg.AccountID, arg.Email, arg.HashedPassword)
	var i User
	err := row.Scan(
		&i.AccountID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Email,
		&i.HashedPassword,
		&i.UserRole,
	)
	return i, err
}

const ExistsUserWithEmail = `-- name: ExistsUserWithEmail :one
SELECT exists (
    SELECT 1 FROM users WHERE LOWER(email) = LOWER($1)
) AS exists
`

func (q *Queries) ExistsUserWithEmail(ctx context.Context, email string) (bool, error) {
	row := q.db.QueryRow(ctx, ExistsUserWithEmail, email)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const GetUserByEmail = `-- name: GetUserByEmail :one
SELECT account_id, created_at, updated_at, email, hashed_password, user_role FROM users WHERE LOWER(email) = LOWER($1)
`

func (q *Queries) GetUserByEmail(ctx context.Context, email string) (User, error) {
	row := q.db.QueryRow(ctx, GetUserByEmail, email)
	var i User
	err := row.Scan(
		&i.AccountID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Email,
		&i.HashedPassword,
		&i.UserRole,
	)
	return i, err
}

const GetUserByID = `-- name: GetUserByID :one
SELECT  u.account_id, u.email, u.user_role, u.created_at , u.updated_at FROM users u WHERE u.account_id = $1
`

type GetUserByIDRow struct {
	AccountID uuid.UUID `json:"account_id"`
	Email     string    `json:"email"`
	UserRole  string    `json:"user_role"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (q *Queries) GetUserByID(ctx context.Context, accountID uuid.UUID) (GetUserByIDRow, error) {
	row := q.db.QueryRow(ctx, GetUserByID, accountID)
	var i GetUserByIDRow
	err := row.Scan(
		&i.AccountID,
		&i.Email,
		&i.UserRole,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const GetUserByIsnID = `-- name: GetUserByIsnID :one
SELECT u.account_id, u.created_at, u.updated_at, u.email, u.hashed_password, u.user_role
FROM users u 
JOIN isn i ON u.account_id = i.user_account_id 
WHERE i.id = $1
`

func (q *Queries) GetUserByIsnID(ctx context.Context, id uuid.UUID) (User, error) {
	row := q.db.QueryRow(ctx, GetUserByIsnID, id)
	var i User
	err := row.Scan(
		&i.AccountID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Email,
		&i.HashedPassword,
		&i.UserRole,
	)
	return i, err
}

const GetUsers = `-- name: GetUsers :many
SELECT u.account_id, u.email, u.user_role, u.created_at , u.updated_at FROM users u
`

type GetUsersRow struct {
	AccountID uuid.UUID `json:"account_id"`
	Email     string    `json:"email"`
	UserRole  string    `json:"user_role"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (q *Queries) GetUsers(ctx context.Context) ([]GetUsersRow, error) {
	rows, err := q.db.Query(ctx, GetUsers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetUsersRow
	for rows.Next() {
		var i GetUsersRow
		if err := rows.Scan(
			&i.AccountID,
			&i.Email,
			&i.UserRole,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const IsFirstUser = `-- name: IsFirstUser :one
SELECT COUNT(*) = 0 AS is_empty
FROM users
`

func (q *Queries) IsFirstUser(ctx context.Context) (bool, error) {
	row := q.db.QueryRow(ctx, IsFirstUser)
	var is_empty bool
	err := row.Scan(&is_empty)
	return is_empty, err
}

const UpdatePassword = `-- name: UpdatePassword :execrows
UPDATE users SET (updated_at, hashed_password) = (NOW(), $2)
WHERE account_id = $1
`

type UpdatePasswordParams struct {
	AccountID      uuid.UUID `json:"account_id"`
	HashedPassword string    `json:"hashed_password"`
}

func (q *Queries) UpdatePassword(ctx context.Context, arg UpdatePasswordParams) (int64, error) {
	result, err := q.db.Exec(ctx, UpdatePassword, arg.AccountID, arg.HashedPassword)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const UpdateUserAccountToAdmin = `-- name: UpdateUserAccountToAdmin :execrows
UPDATE users 
SET 
    user_role = 'admin'
WHERE 
    account_id = $1
`

func (q *Queries) UpdateUserAccountToAdmin(ctx context.Context, accountID uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, UpdateUserAccountToAdmin, accountID)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const UpdateUserAccountToMember = `-- name: UpdateUserAccountToMember :execrows
UPDATE users 
SET 
    user_role = 'member'
WHERE 
    account_id = $1
`

func (q *Queries) UpdateUserAccountToMember(ctx context.Context, accountID uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, UpdateUserAccountToMember, accountID)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}
