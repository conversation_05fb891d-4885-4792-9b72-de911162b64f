package auth

type ErrorPageData struct {
	Title   string
	Message string
}

templ ErrorPage(data ErrorPageData) {
	<!DOCTYPE html>
	<html>
		<head>
			<title>{ data.Title }</title>
			<style>
				body {
					font-family: Arial, sans-serif;
					margin: 40px;
					background-color: #f5f5f5;
				}
				.container {
					max-width: 600px;
					margin: 0 auto;
					background: white;
					padding: 40px;
					border-radius: 8px;
					box-shadow: 0 2px 10px rgba(0,0,0,0.1);
				}
				.error {
					color: #d32f2f;
				}
				h1 {
					color: #333;
					margin-bottom: 20px;
				}
				p {
					color: #666;
					line-height: 1.6;
				}
			</style>
		</head>
		<body>
			<div class="container">
				<h1 class="error">{ data.Title }</h1>
				<p>{ data.Message }</p>
			</div>
		</body>
	</html>
}
